import { Job, Worker } from 'bullmq'
import { RedisDB } from '../../../../../../model/redis/redis'
import { TaskName } from '../type'
import { CheckPreCourseCompletionTask } from '../task/checkPreCourseCompletion'
import { ITask } from '../../../schedule/type'
import logger from '../../../../../../model/logger/logger'
import { GroupSend } from '../task/groupSend'
import { ChatHistoryService } from '../../../chat_history/chat_history'
import { sleep } from '../../../../../../lib/schedule/schedule'
import { ChatStatStoreManager } from '../../../../storage/chat_state_store'
import { DataService } from '../../../../getter/getData'
import { ChatDB } from '../../../../database/chat'
import { MessageSender } from '../../../message/message_send'
import { Config } from '../../../../../../config/config'
import { XiaoHongShuActivationReminderTask } from '../task/xiaohongshuActivationTask'
import { PhoneBindTask } from '../task/phoneBind'
import { Sema } from 'async-sema'
import { AsyncLock } from '../../../../../../lib/lock/lock'

export const sema = new Sema(15)

export class MoerProcessor {
  private static workerMap: Map<string, Worker> = new Map()

  public static startGeneral(generalSopKey: string) {
    let worker = this.workerMap.get(generalSopKey)

    if (!worker) {
      worker = new Worker(generalSopKey, async (job: Job) => {
        try {
          await MoerProcessor.generalProcess(job)
        } catch (e) {
          console.error('任务执行出错:', job.name, job.data, e)
          logger.error(e)
        }
      }, {
        connection: RedisDB.getInstance(),
        lockDuration: 60 * 1000,
        concurrency: 30,
        removeOnComplete: {
          age: 3600, // keep up to 1 hour
          count: 1000, // keep up to 1000 jobs
        },
        removeOnFail: {
          age: 24 * 3600, // keep up to 24 hours
        }
      })

      worker.on('error', (err) => {
        logger.error('SOP Worker 发生未捕获错误', err)
      })

      this.workerMap.set(generalSopKey, worker)
    }

    return worker
  }



  public static async generalProcess(job: Job) {
    if (job.opts.delay && job.opts.delay < 0) {
      // logger.warn(`任务 ${job.name} 超时 ${-job.opts.delay} 毫秒，不进行处理`)
      return
    }

    job =  job as Job<ITask>
    // 加锁，防止在 主动回复中间插入消息
    const lock = new AsyncLock()
    try {
      await sema.acquire()

      await lock.acquire(job.data.chatId, async () => {

        // 如果是 空状态的话，进行一下状态恢复
        await ChatStatStoreManager.initState(job.data.chatId)

        const chat = await ChatDB.getById(job.data.chatId)
        if (chat && chat.is_stop_group_push) {
          logger.log({ chat_id: chat.id }, '发送 SOP Flag 已关闭')
          return
        }

        if (!chat) {
          return
        }

        // 如果最后一条消息是非营销消息，时间在 1 分钟内，非闲聊节点，延迟进行消息发送。
        const isLastMsgWithin1Minute = await ChatHistoryService.isLastMessageWithDuration(chat.id, 1, 'minute')
        if (isLastMsgWithin1Minute) {
          await sleep(60 * 1000)
        }

        const task = job.data

        // 线上测试账号，标注下任务发送时间
        if (Config.isOnlineTestAccount() && task.scheduleTime) {
          await MessageSender.sendById({
            user_id: task.userId,
            chat_id: task.chatId,
            ai_msg: `${task.name} ${task.scheduleTime.is_course_week ? '上课周' : '非上课周'} Day${task.scheduleTime.day} ${task.scheduleTime.time}`,
          }, { notAddBotMessage: true })
        }

        switch (job.name) {
          case TaskName.PhoneBindCheck:
            await new PhoneBindTask().phoneBindCheck(job.data)
            break
          case TaskName.AddPhoneBindTask:
            await PhoneBindTask.addBindPhoneTask(job.data)
            break
          case TaskName.XiaoHongShuActivationReminder:
            await new XiaoHongShuActivationReminderTask().process(job.data)
            break
          case TaskName.CheckPreCourseCompletion:
            // await new CheckPreCourseCompletionTask().process(job.data as ITask)
            break
          default:
            // await new GroupSend().process(job.data as ITask)
        }

        const data = job.data as ITask

        /**
       * 特别注意，这里一定要做入库操作，否则状态不会被存库，一旦客户发送信息，会从数据库取信息，当前未保存的状态会直接被替换掉
       */
        await DataService.saveChat(data.chatId, data.userId)
      }, { timeout: 70 * 1000 })
    } catch (e) {
      logger.error(e)
    } finally {
      sema.release()
    }
  }
}